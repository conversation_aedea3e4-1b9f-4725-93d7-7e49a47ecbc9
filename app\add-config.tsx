import { router, useNavigation } from 'expo-router';
import React, { useState, useEffect } from 'react';

import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { ConfigType, configTypes } from '@/lib/types';
import { SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { QrCode } from 'lucide-react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as WebBrowser from 'expo-web-browser';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const [showCamera, setShowCamera] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const [isScanning, setIsScanning] = useState(false);

  const handleTypeSelect = (type: ConfigType) => {
    router.push({
      pathname: `/config-form/${type}`,
    })
  };

  const handleQRCodeScan = async () => {
    if (!permission?.granted) {
      const result = await requestPermission();
      if (!result.granted) {
        return;
      }
    }
    setIsScanning(false); // 重置扫描状态
    setShowCamera(true);
  };

  const handleGenerateQRCode = async () => {
    try {
      // 打开系统浏览器到指定网址
      await WebBrowser.openBrowserAsync('https://www.qr-code-generator.com/');
    } catch (error) {
      console.error('Failed to open browser:', error);
    }
  };

  const handleBarcodeScanned = ({ data }: { data: string }) => {
    // 防止重复扫描
    if (isScanning) {
      return;
    }

    setIsScanning(true);
    setShowCamera(false);

    // 尝试解析二维码JSON数据并确定配置类型
    try {
      const parsed = JSON.parse(data);
      let configType: ConfigType = '3x-ui'; // 默认类型

      // 如果JSON中包含type字段且是有效的配置类型，使用该类型
      if (parsed.type && configTypes.includes(parsed.type)) {
        configType = parsed.type;
      }

      router.push({
        pathname: `/config-form/${configType}`,
        params: {
          initialData: data
        }
      });
    } catch (error) {
      console.error('Failed to parse QR code data:', error);
      // 如果解析失败，默认跳转到3x-ui配置页面，将原始数据传递
      router.push({
        pathname: `/config-form/3x-ui`,
        params: {
          initialData: data
        }
      });
    }
  };

  // 设置header右侧的扫描按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity onPress={handleQRCodeScan} style={{ marginRight: 4 }}>
          <QrCode size={20} color={textColor} />
        </TouchableOpacity>
      ),
    });
  }, [navigation, textColor]);

  if (showCamera) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <CameraView
          style={styles.camera}
          facing="back"
          onBarcodeScanned={handleBarcodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ['qr'],
          }}
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.scanArea} />
            <Text style={styles.scanText}>{t('common.scanQRCode')}</Text>
            <TouchableOpacity
              style={styles.generateButton}
              onPress={handleGenerateQRCode}
            >
              <Text style={styles.generateButtonText}>生成二维码</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setIsScanning(false); // 重置扫描状态
                setShowCamera(false);
              }}
            >
              <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </CameraView>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <View style={styles.typeList}>
          {configTypes.map((configType) => (
            <View key={configType}>

              <TouchableOpacity
                style={styles.typeItem}
                onPress={() => handleTypeSelect(configType)}
              >
                <Text style={[styles.title, { color: textColor }]}>
                  {t(`configTypes.${configType}`)}
                </Text>
              </TouchableOpacity>
              <View style={[styles.separator, { backgroundColor: borderColor }]} />
            </View>
          ))}
        </View>
      </ScrollView>


    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
    padding: 0,
  },
  typeList: {
    flex: 1,
  },
  typeItem: {
    paddingVertical: 12,  // 参考3x-ui的12px垂直边距
    paddingHorizontal: 12, // 参考3x-ui的12px水平边距
    // backgroundColor will inherit from parent
  },
  title: {
    fontSize: 16,         // 参考3x-ui的16px字体大小
    fontWeight: '600',    // 参考3x-ui的600字体权重
  },
  separator: {
    height: 1,            // 参考3x-ui的1px分隔线高度
    // backgroundColor will be set dynamically
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#fff',
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  scanText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 20,
    textAlign: 'center',
  },
  generateButton: {
    marginTop: 30,
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
  },
  generateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    marginTop: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
